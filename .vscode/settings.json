{
	"editor.formatOnSave": true,
	"[javascript][typescript]": {
		"editor.defaultFormatter": "biomejs.biome",
		"editor.formatOnSave": true
	},
	"editor.rulers": [100],
	"editor.codeActionsOnSave": [
		"source.addMissingImports",
		"source.fixAll",
		"source.organizeImports"
	],
	"biome.lsp.bin": "node_modules/@biomejs/cli-darwin-arm64/biome",
	// Show in vscode "Problems" tab when there are errors
	"typescript.tsserver.experimental.enableProjectDiagnostics": true,
	// Use absolute import for typescript files
	"typescript.preferences.importModuleSpecifier": "non-relative"
}
